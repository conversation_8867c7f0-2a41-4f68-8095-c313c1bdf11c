# Entry point for Aura Inventory System server

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pathlib import Path
import sqlite3
import asyncio
import logging
import json
import base64
import tempfile
import os
import subprocess
from datetime import datetime
from typing import Dict, Any, List

# Import our services
from database.manager import DatabaseManager
from services.command_processor import CommandProcessor
from services.asr import ASRService
from services.enhanced_asr_service import EnhancedASRService
from services.llm_service import LLMService

# Load configuration
def load_config():
    """Load configuration from config.json file."""
    config_path = Path("./config.json")
    if config_path.exists():
        with open(config_path, 'r') as f:
            return json.load(f)
    else:
        logger.warning("config.json not found. Using default configuration.")
        return {
            "defaults": {
                "llm": {"model": "phi-3-mini", "engine": "mlx"},
                "asr": {"model": "mlx-whisper-medium-mlx", "backend_preference": "mlx_whisper"},
                "tts": {"voice": "af_nova"}
            }
        }

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Aura Inventory System", version="1.0.0")

# Serve static files
app.mount("/static", StaticFiles(directory="./static"), name="static")

# Serve the client page
@app.get("/client")
async def get_client():
    """Serve the main client HTML page."""
    return FileResponse("./client.html")

# Path to database file
DB_FILE = Path("./database/aura.db")

# Global service instances
db_manager = None
command_processor = None
asr_service = None
enhanced_asr_service = None
llm_service = None

# Utilities
async def init_db():
    """Initialize the SQLite database if it doesn't exist."""
    # Create database directory if it doesn't exist
    DB_FILE.parent.mkdir(exist_ok=True)
    
    if not DB_FILE.exists():
        conn = sqlite3.connect(DB_FILE)
        with open('./database/schema.sql', 'r') as f:
            conn.executescript(f.read())
        conn.close()
        logger.info("Database initialized.")
    else:
        logger.info("Database already exists.")

@app.on_event("startup")
async def startup_event():
    global db_manager, command_processor, asr_service, enhanced_asr_service, llm_service
    
    # Load configuration
    config = load_config()
    defaults = config.get('defaults', {})
    
    await init_db()
    
    # Initialize services with config defaults
    db_manager = DatabaseManager(str(DB_FILE))
    command_processor = CommandProcessor(db_manager)
    asr_service = ASRService()
    
    # Initialize Enhanced ASR with default model
    asr_defaults = defaults.get('asr', {})
    enhanced_asr_service = EnhancedASRService(auto_select=True)
    if asr_defaults.get('model'):
        try:
            enhanced_asr_service.switch_model(asr_defaults['model'])
            logger.info(f"ASR service initialized with default model: {asr_defaults['model']}")
        except Exception as e:
            logger.warning(f"Failed to set default ASR model {asr_defaults['model']}: {e}")
    
    # Initialize LLM service with default model and share it with command processor
    llm_defaults = defaults.get('llm', {})
    llm_service = command_processor.nlu_service.llm_service  # Use the same instance
    try:
        # LLM service is already initialized by the command processor
        if llm_defaults.get('model'):
            success = llm_service.switch_model(llm_defaults['model'])
            if success:
                logger.info(f"LLM service initialized with default model: {llm_defaults['model']}")
            else:
                logger.warning(f"Failed to set default LLM model: {llm_defaults['model']}")
        else:
            logger.info("LLM service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize LLM service: {e}")
    
    # Initialize TTS with default voice
    tts_defaults = defaults.get('tts', {})
    if tts_defaults.get('voice'):
        try:
            command_processor.tts_service.set_voice(tts_defaults['voice'])
            logger.info(f"TTS service initialized with default voice: {tts_defaults['voice']}")
        except Exception as e:
            logger.warning(f"Failed to set default TTS voice {tts_defaults['voice']}: {e}")
    
    logger.info("Aura Inventory System started successfully with configuration defaults")

@app.get("/")
async def root():
    """Health check endpoint."""
    return {"message": "Aura Inventory System is running", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """System health check."""
    try:
        # Check database connection
        summary = db_manager.get_inventory_summary()
        return {
            "status": "healthy",
            "database": "connected",
            "inventory_summary": summary
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "unhealthy", "error": str(e)}

# Global connection managers for different WebSocket streams
class ConnectionManager:
    def __init__(self):
        self.main_connections: List[WebSocket] = []
        self.audio_connections: List[WebSocket] = []
        self.context_connections: List[WebSocket] = []
    
    async def connect_main(self, websocket: WebSocket):
        await websocket.accept()
        self.main_connections.append(websocket)
    
    async def connect_audio(self, websocket: WebSocket):
        await websocket.accept()
        self.audio_connections.append(websocket)
    
    async def connect_context(self, websocket: WebSocket):
        await websocket.accept()
        self.context_connections.append(websocket)
    
    def disconnect_main(self, websocket: WebSocket):
        if websocket in self.main_connections:
            self.main_connections.remove(websocket)
    
    def disconnect_audio(self, websocket: WebSocket):
        if websocket in self.audio_connections:
            self.audio_connections.remove(websocket)
    
    def disconnect_context(self, websocket: WebSocket):
        if websocket in self.context_connections:
            self.context_connections.remove(websocket)
    
    async def send_to_audio(self, message: dict):
        if self.audio_connections:
            dead_connections = []
            for connection in self.audio_connections:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    dead_connections.append(connection)
            
            for connection in dead_connections:
                self.disconnect_audio(connection)
    
    async def send_to_context(self, message: dict):
        if self.context_connections:
            dead_connections = []
            for connection in self.context_connections:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    dead_connections.append(connection)
            
            for connection in dead_connections:
                self.disconnect_context(connection)

manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for command processing and control messages."""
    await manager.connect_main(websocket)
    logger.info("Main WebSocket connection established")
    
    try:
        # Send initial system status
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 200,
            "service": "WebSocket",
            "message": "Connected to Aura Inventory System"
        }))
        
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_websocket_message(websocket, message)
            except json.JSONDecodeError:
                # Handle plain text messages (for testing)
                await handle_text_command(websocket, data)
                
    except WebSocketDisconnect:
        manager.disconnect_main(websocket)
        logger.info("Main WebSocket connection closed")
    except Exception as e:
        logger.error(f"Main WebSocket error: {e}")
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 500,
            "service": "WebSocket",
            "message": f"Error: {str(e)}"
        }))

@app.websocket("/ws/audio")
async def audio_websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for audio data streaming."""
    await manager.connect_audio(websocket)
    logger.info("Audio WebSocket connection established")
    
    try:
        while True:
            # This endpoint only handles audio upload/download
            data = await websocket.receive_text()
            message = json.loads(data)
            await handle_audio_message(websocket, message)
    except WebSocketDisconnect:
        manager.disconnect_audio(websocket)
        logger.info("Audio WebSocket connection closed")
    except Exception as e:
        logger.error(f"Audio WebSocket error: {e}")

@app.websocket("/ws/context")
async def context_websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for detailed context data (prompts, debug info, etc.)."""
    await manager.connect_context(websocket)
    logger.info("Context WebSocket connection established")
    
    try:
        while True:
            # This endpoint only sends context/debug information
            await websocket.receive_text()  # Keep connection alive
    except WebSocketDisconnect:
        manager.disconnect_context(websocket)
        logger.info("Context WebSocket connection closed")
    except Exception as e:
        logger.error(f"Context WebSocket error: {e}")

async def handle_audio_message(websocket: WebSocket, message: Dict[str, Any]):
    """Handle audio-specific WebSocket messages."""
    message_type = message.get("type")
    
    if message_type == "AUDIO_COMMAND":
        # Handle base64 audio data
        audio_data = message.get("audio_data")
        if audio_data:
            await process_audio_command_stream(websocket, audio_data)
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "ASR",
                "message": "No audio data provided"
            }))

async def process_audio_command_stream(websocket: WebSocket, audio_data_base64: str):
    """Process an audio command through the ASR pipeline via audio stream."""
    try:
        logger.info(f"Processing audio command via stream: {len(audio_data_base64)} chars of base64 data")
        
        # Validate base64 string length
        if len(audio_data_base64) < 10:
            logger.warning(f"Base64 audio data too short: {len(audio_data_base64)} chars")
            await websocket.send_text(json.dumps({
                "type": "TRANSCRIPTION_ERROR",
                "error": "Audio data too short"
            }))
            return
        
        # Ensure proper base64 padding
        while len(audio_data_base64) % 4:
            audio_data_base64 += '='
        
        # Decode the base64 audio data
        try:
            audio_data = base64.b64decode(audio_data_base64, validate=True)
        except Exception as decode_error:
            logger.error(f"Base64 decode error: {decode_error}")
            await websocket.send_text(json.dumps({
                "type": "TRANSCRIPTION_ERROR",
                "error": "Invalid audio data format"
            }))
            return
            
        logger.info(f"Decoded audio data: {len(audio_data)} bytes")

        # Save WebM audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=".webm", delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_webm_path = temp_file.name
        
        logger.info(f"Saved WebM audio to: {temp_webm_path}")

        # Convert WebM to WAV using FFmpeg
        temp_wav_path = temp_webm_path.replace('.webm', '.wav')
        ffmpeg_cmd = [
            'ffmpeg', '-y', '-i', temp_webm_path, 
            '-ar', '16000', '-ac', '1', '-acodec', 'pcm_s16le',
            '-loglevel', 'error',  # Suppress FFmpeg output
            temp_wav_path
        ]
        
        logger.info(f"Running FFmpeg command: {' '.join(ffmpeg_cmd)}")
        
        try:
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info(f"Successfully converted to WAV: {temp_wav_path}")
                
                # Send partial transcription update via audio stream
                await websocket.send_text(json.dumps({
                    "type": "TRANSCRIPTION_PARTIAL",
                    "text": "Processing audio"
                }))
                
                # Use enhanced ASR service to process audio file
                result = enhanced_asr_service.transcribe_file(temp_wav_path)
                transcript = result.get('transcript', 'Transcription failed')
                logger.info(f"ASR transcript: {transcript}")
                logger.info(f"ASR processing time: {result.get('processing_time', 0):.3f}s")
                logger.info(f"ASR model used: {result.get('model', 'Unknown')}")
                
                # Send final transcription via audio stream
                await websocket.send_text(json.dumps({
                    "type": "TRANSCRIPTION_FINAL",
                    "text": transcript
                }))
                
                # Clean up
                os.unlink(temp_webm_path)
                os.unlink(temp_wav_path)
                
                # Now process the transcript through main pipeline
                # We need to notify main websocket about the transcript
                if transcript and manager.main_connections:
                    for main_ws in manager.main_connections:
                        try:
                            await main_ws.send_text(json.dumps({
                                "type": "AUDIO_TRANSCRIPTION_READY",
                                "transcript": transcript
                            }))
                        except:
                            pass  # Connection might be dead
                            
            else:
                logger.error(f"FFmpeg conversion failed (code {result.returncode}): {result.stderr}")
                await websocket.send_text(json.dumps({
                    "type": "TRANSCRIPTION_ERROR",
                    "error": "Audio conversion failed"
                }))
                os.unlink(temp_webm_path)
        
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg conversion timeout")
            await websocket.send_text(json.dumps({
                "type": "TRANSCRIPTION_ERROR",
                "error": "Audio conversion timeout"
            }))
            os.unlink(temp_webm_path)
        except Exception as e:
            logger.error(f"FFmpeg conversion error: {e}")
            await websocket.send_text(json.dumps({
                "type": "TRANSCRIPTION_ERROR", 
                "error": "Audio conversion error"
            }))
            if os.path.exists(temp_webm_path):
                os.unlink(temp_webm_path)

    except Exception as e:
        logger.error(f"Error processing audio command via stream: {e}")
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 500,
            "service": "ASR",
            "message": f"Error: {str(e)}"
        }))

async def handle_websocket_message(websocket: WebSocket, message: Dict[str, Any]):
    """Handle structured WebSocket messages."""
    message_type = message.get("type")
    
    if message_type == "VOICE_COMMAND":
        # Handle text transcript directly
        transcript = message.get("transcript")
        if transcript:
            await process_voice_command(websocket, transcript)
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "ASR",
                "message": "No transcript provided"
            }))
    
    elif message_type == "AUDIO_COMMAND":
        # Handle base64 audio data
        audio_data = message.get("audio_data")
        if audio_data:
            await process_audio_command(websocket, audio_data)
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "ASR",
                "message": "No audio data provided"
            }))
    
    elif message_type == "GET_AVAILABLE_MODELS":
        # Get available models
        try:
            models = llm_service.get_available_models()
            await websocket.send_text(json.dumps({
                "type": "AVAILABLE_MODELS",
                "models": models
            }))
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 500,
                "service": "LLM",
                "message": f"Error getting models: {str(e)}"
            }))
    
    elif message_type == "GET_CURRENT_MODEL":
        # Get current model
        try:
            current_model = llm_service.get_current_model()
            await websocket.send_text(json.dumps({
                "type": "CURRENT_MODEL",
                "model_name": current_model
            }))
        except Exception as e:
            logger.error(f"Error getting current model: {e}")
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 500,
                "service": "LLM",
                "message": f"Error getting current model: {str(e)}"
            }))
    
    elif message_type == "SWITCH_MODEL":
        # Switch model
        model_name = message.get("model_name")
        if model_name:
            try:
                success = llm_service.switch_model(model_name)
                if success:
                    await websocket.send_text(json.dumps({
                        "type": "MODEL_SWITCHED",
                        "model_name": model_name
                    }))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "SYSTEM_STATUS",
                        "status_code": 400,
                        "service": "LLM",
                        "message": f"Failed to switch to model: {model_name}"
                    }))
            except Exception as e:
                logger.error(f"Error switching model: {e}")
                await websocket.send_text(json.dumps({
                    "type": "SYSTEM_STATUS",
                    "status_code": 500,
                    "service": "LLM",
                    "message": f"Error switching model: {str(e)}"
                }))
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "LLM",
                "message": "No model name provided"
            }))

    elif message_type == "GET_AVAILABLE_VOICES":
        # Get available TTS voices
        try:
            voices = command_processor.tts_service.get_available_voices()
            voice_info = {}
            for voice in voices:
                voice_info[voice] = command_processor.tts_service.get_voice_info(voice)
            await websocket.send_text(json.dumps({
                "type": "AVAILABLE_VOICES",
                "voices": voice_info
            }))
        except Exception as e:
            logger.error(f"Error getting available voices: {e}")
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 500,
                "service": "TTS",
                "message": f"Error getting voices: {str(e)}"
            }))

    elif message_type == "GET_CURRENT_VOICE":
        # Get current TTS voice
        try:
            current_voice = command_processor.tts_service.voice
            await websocket.send_text(json.dumps({
                "type": "CURRENT_VOICE",
                "voice_name": current_voice
            }))
        except Exception as e:
            logger.error(f"Error getting current voice: {e}")
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 500,
                "service": "TTS",
                "message": f"Error getting current voice: {str(e)}"
            }))

    elif message_type == "SWITCH_VOICE":
        # Switch TTS voice
        voice_name = message.get("voice_name")
        if voice_name:
            try:
                success = command_processor.tts_service.set_voice(voice_name)
                if success:
                    await websocket.send_text(json.dumps({
                        "type": "VOICE_SWITCHED",
                        "voice_name": voice_name
                    }))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "SYSTEM_STATUS",
                        "status_code": 400,
                        "service": "TTS",
                        "message": f"Failed to switch to voice: {voice_name}"
                    }))
            except Exception as e:
                logger.error(f"Error switching voice: {e}")
                await websocket.send_text(json.dumps({
                    "type": "SYSTEM_STATUS",
                    "status_code": 500,
                    "service": "TTS",
                    "message": f"Error switching voice: {str(e)}"
                }))
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "TTS",
                "message": "No voice name provided"
            }))
    
    elif message_type == "GET_AVAILABLE_ASR_MODELS":
        # Get available ASR models
        try:
            models = enhanced_asr_service.get_available_models()
            model_dict = {}
            for model in models:
                model_dict[model['name']] = model
            await websocket.send_text(json.dumps({
                "type": "AVAILABLE_ASR_MODELS",
                "models": model_dict
            }))
        except Exception as e:
            logger.error(f"Error getting available ASR models: {e}")
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 500,
                "service": "ASR",
                "message": f"Error getting ASR models: {str(e)}"
            }))
    
    elif message_type == "GET_CURRENT_ASR_MODEL":
        # Get current ASR model
        try:
            current_model_info = enhanced_asr_service.get_current_model_info()
            model_name = current_model_info.get('name', 'Unknown')
            await websocket.send_text(json.dumps({
                "type": "CURRENT_ASR_MODEL",
                "model_name": model_name
            }))
        except Exception as e:
            logger.error(f"Error getting current ASR model: {e}")
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 500,
                "service": "ASR",
                "message": f"Error getting current ASR model: {str(e)}"
            }))
    
    elif message_type == "SWITCH_ASR_MODEL":
        # Switch ASR model
        model_name = message.get("model_name")
        if model_name:
            try:
                success = enhanced_asr_service.switch_model(model_name)
                if success:
                    await websocket.send_text(json.dumps({
                        "type": "ASR_MODEL_SWITCHED",
                        "model_name": model_name
                    }))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "SYSTEM_STATUS",
                        "status_code": 400,
                        "service": "ASR",
                        "message": f"Failed to switch to ASR model: {model_name}"
                    }))
            except Exception as e:
                logger.error(f"Error switching ASR model: {e}")
                await websocket.send_text(json.dumps({
                    "type": "SYSTEM_STATUS",
                    "status_code": 500,
                    "service": "ASR",
                    "message": f"Error switching ASR model: {str(e)}"
                }))
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "ASR",
                "message": "No ASR model name provided"
            }))
    
    elif message_type == "OPTIMIZE_ASR_MODEL":
        # Optimize ASR model for specific use case
        use_case = message.get("use_case")
        if use_case:
            try:
                success = enhanced_asr_service.optimize_for_use_case(use_case)
                if success:
                    current_model_info = enhanced_asr_service.get_current_model_info()
                    model_name = current_model_info.get('name', 'Unknown')
                    await websocket.send_text(json.dumps({
                        "type": "ASR_OPTIMIZED",
                        "model_name": model_name,
                        "use_case": use_case
                    }))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "SYSTEM_STATUS",
                        "status_code": 400,
                        "service": "ASR",
                        "message": f"Failed to optimize ASR for: {use_case}"
                    }))
            except Exception as e:
                logger.error(f"Error optimizing ASR model: {e}")
                await websocket.send_text(json.dumps({
                    "type": "SYSTEM_STATUS",
                    "status_code": 500,
                    "service": "ASR",
                    "message": f"Error optimizing ASR model: {str(e)}"
                }))
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "ASR",
                "message": "No use case provided"
            }))
    
    elif message_type == "SYSTEM_STATUS":
        # Echo system status
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 200,
            "service": "Server",
            "message": "System operational"
        }))
    
    else:
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 400,
            "service": "WebSocket",
            "message": f"Unknown message type: {message_type}"
        }))

async def handle_text_command(websocket: WebSocket, text: str):
    """Handle plain text commands (for testing)."""
    logger.info(f"Processing text command: {text}")
    await process_voice_command(websocket, text)

async def process_voice_command(websocket: WebSocket, transcript: str):
    """Process a voice command through the complete pipeline."""
    try:
        logger.info(f"Processing voice command: {transcript}")
        
        # Process the command through the pipeline
        audio_response, visual_response = command_processor.process_voice_command(transcript)
        
        # Send lightweight audio response (without audio data)
        audio_response_light = {
            "type": audio_response["type"],
            "text": audio_response["text"],
            "success": audio_response.get("success", True),
            "audio_skipped": audio_response.get("audio_skipped", False),
            "has_audio": audio_response.get("audio_data") is not None
        }
        await websocket.send_text(json.dumps(audio_response_light))
        
        # Send audio data to dedicated audio stream if available
        if audio_response.get("audio_data"):
            audio_stream_data = {
                "type": "AUDIO_DATA",
                "audio_data": audio_response["audio_data"],
                "transcript": transcript[:50] + "..." if len(transcript) > 50 else transcript
            }
            await manager.send_to_audio(audio_stream_data)
        
        # Send visual response
        await websocket.send_text(json.dumps(visual_response))
        
        # Enhanced context response with full LLM prompt and debug info
        context_data = {
            "transcript": transcript,  # Full transcript
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "audio_text_length": len(audio_response.get("text", "")),
                "has_audio": audio_response.get("audio_data") is not None,
                "audio_skipped": audio_response.get("audio_skipped", False),
                "visual_type": visual_response.get("data_type", "unknown"),
                "success": audio_response.get("success", True)
            },
            "chain_status": "✅ Complete" if audio_response.get("success") else "❌ Error"
        }
        
        # Include SQL queries if available
        sql_queries = audio_response.get('_sql_queries', [])
        if sql_queries:
            context_data["sql_queries"] = sql_queries
        
        # Include FULL LLM prompt and raw response (no truncation)
        llm_prompt = audio_response.get('_llm_prompt')
        if llm_prompt:
            context_data["llm_prompt"] = llm_prompt  # Full prompt, no truncation
        
        llm_raw_response = audio_response.get('_llm_raw_response')
        if llm_raw_response:
            context_data["llm_raw_response"] = llm_raw_response  # Full response, no truncation
        
        # Include item_list JSON if available
        if visual_response.get("data_type") == "item_list" and visual_response.get("payload"):
            context_data["item_list"] = visual_response["payload"]
        
        # Include HTML content if available
        if visual_response.get("data_type") == "html" and visual_response.get("payload", {}).get("html"):
            context_data["html_content"] = visual_response["payload"]["html"]
        
        context_response = {
            "type": "CONTEXT_RESPONSE",
            "data": context_data
        }
        
        # Send context to dedicated context stream
        await manager.send_to_context(context_response)
        
    except Exception as e:
        logger.error(f"Error processing voice command: {e}")
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 500,
            "service": "CommandProcessor",
            "message": f"Error: {str(e)}"
        }))

async def process_audio_command(websocket: WebSocket, audio_data_base64: str):
    """Process an audio command through the ASR pipeline."""
    try:
        logger.info(f"Processing audio command: {len(audio_data_base64)} chars of base64 data")
        
        # Decode the base64 audio data
        audio_data = base64.b64decode(audio_data_base64)
        logger.info(f"Decoded audio data: {len(audio_data)} bytes")

        # Save WebM audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=".webm", delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_webm_path = temp_file.name
        
        logger.info(f"Saved WebM audio to: {temp_webm_path}")

        # Convert WebM to WAV using FFmpeg
        temp_wav_path = temp_webm_path.replace('.webm', '.wav')
        ffmpeg_cmd = [
            'ffmpeg', '-y', '-i', temp_webm_path, 
            '-ar', '16000', '-ac', '1', '-acodec', 'pcm_s16le',
            '-loglevel', 'error',  # Suppress FFmpeg output
            temp_wav_path
        ]
        
        logger.info(f"Running FFmpeg command: {' '.join(ffmpeg_cmd)}")
        
        try:
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info(f"Successfully converted to WAV: {temp_wav_path}")
                
                # Send partial transcription update
                await websocket.send_text(json.dumps({
                    "type": "TRANSCRIPTION_PARTIAL",
                    "text": "Processing audio"
                }))
                
                # Use enhanced ASR service to process audio file
                result = enhanced_asr_service.transcribe_file(temp_wav_path)
                transcript = result.get('transcript', 'Transcription failed')
                logger.info(f"ASR transcript: {transcript}")
                logger.info(f"ASR processing time: {result.get('processing_time', 0):.3f}s")
                logger.info(f"ASR model used: {result.get('model', 'Unknown')}")
                
                # Send final transcription
                await websocket.send_text(json.dumps({
                    "type": "TRANSCRIPTION_FINAL",
                    "text": transcript
                }))
                
                # Clean up
                os.unlink(temp_webm_path)
                os.unlink(temp_wav_path)
            else:
                logger.error(f"FFmpeg conversion failed (code {result.returncode}): {result.stderr}")
                transcript = "Audio conversion failed"
                os.unlink(temp_webm_path)
        
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg conversion timeout")
            transcript = "Audio conversion timeout"
            os.unlink(temp_webm_path)
        except Exception as e:
            logger.error(f"FFmpeg conversion error: {e}")
            transcript = "Audio conversion error"
            if os.path.exists(temp_webm_path):
                os.unlink(temp_webm_path)

        if transcript:
            await process_voice_command(websocket, transcript)
        else:
            await websocket.send_text(json.dumps({
                "type": "SYSTEM_STATUS",
                "status_code": 400,
                "service": "ASR",
                "message": "Audio processing failed"
            }))

    except Exception as e:
        logger.error(f"Error processing audio command: {e}")
        await websocket.send_text(json.dumps({
            "type": "SYSTEM_STATUS",
            "status_code": 500,
            "service": "ASR",
            "message": f"Error: {str(e)}"
        }))


if __name__ == "__main__":
    # Load configuration for server settings
    config = load_config()
    server_config = config.get('server', {})
    
    host = server_config.get('host', '0.0.0.0')
    port = server_config.get('port', 8000)
    debug = server_config.get('debug', False)
    
    uvicorn.run("server:app", host=host, port=port, log_level="info", reload=debug)

