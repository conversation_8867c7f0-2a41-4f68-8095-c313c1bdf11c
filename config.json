{"defaults": {"llm": {"model": "phi-3-mini", "temperature": 0.3, "max_tokens": 1024, "engine": "mlx", "timeout_seconds": 30, "max_retries": 2}, "asr": {"model": "mlx-community/whisper-medium-mlx", "language": "en", "backend_preference": "mlx_whisper", "priority": "balanced"}, "tts": {"voice": "af_nova", "speed": 1.0, "volume": 1.0}}, "logging": {"level": "INFO", "sql_logging": true, "sql_log_file": "logs/sql_queries.log"}, "server": {"host": "0.0.0.0", "port": 8000, "debug": false, "websocket_timeout": 60, "websocket_ping_interval": 20, "websocket_ping_timeout": 10}}