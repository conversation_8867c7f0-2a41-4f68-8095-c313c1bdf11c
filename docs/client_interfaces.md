# Enhanced Client Interfaces for Aura

This document describes the enhanced client interfaces for <PERSON><PERSON><PERSON> (ASR) and <PERSON> (TTS) in the Aura inventory system.

## Overview

The Aura system now includes robust client interfaces for both speech recognition and text-to-speech functionality:

- **WhisperClient**: Enhanced interface for whisper.cpp with advanced configuration options
- **PiperClient**: High-quality TTS interface for <PERSON> with voice management
- **Enhanced ASRService**: Improved ASR service using WhisperClient
- **Enhanced TTSService**: Improved TTS service using PiperClient with fallback support

## Architecture

```
services/
├── clients/
│   ├── __init__.py
│   ├── whisper_client.py    # Enhanced Whisper client
│   └── piper_client.py      # Enhanced Piper client
├── asr.py                   # Enhanced ASR service
├── tts.py                   # Enhanced TTS service
├── nlu.py                   # Natural language understanding
├── advanced_nlu.py          # Advanced NLU implementation
└── command_processor.py     # Command processing pipeline
```

## WhisperClient

### Features

- **Model Management**: Support for different Whisper model sizes (tiny, base, small, medium, large)
- **Advanced Configuration**: Fine-tuning parameters for temperature, beam search, thresholds
- **Multiple Output Formats**: Text, JSON, SRT, VTT, CSV output support
- **Robust Error Handling**: Comprehensive error reporting and fallback modes
- **Buffer Processing**: Direct audio buffer transcription without file I/O

### Usage

```python
from services.clients.whisper_client import WhisperClient, WhisperConfig, WhisperModel

# Basic usage
client = WhisperClient()
result = client.transcribe_file("audio.wav")
print(result["transcript"])

# Advanced configuration
config = WhisperConfig(
    model=WhisperModel.SMALL,
    language="en",
    temperature=0.1,
    beam_search=True,
    no_timestamps=True
)
client = WhisperClient(config)

# Audio buffer processing
audio_buffer = b"..."  # Raw audio data
result = client.transcribe_buffer(audio_buffer, sample_rate=16000)
```

### Configuration Options

- `model`: Whisper model size (TINY, BASE, SMALL, MEDIUM, LARGE, etc.)
- `language`: Target language code (default: "en")
- `temperature`: Sampling temperature (0.0-1.0)
- `threads`: Number of processing threads
- `beam_search`: Enable beam search for better accuracy
- `no_timestamps`: Disable timestamp generation
- `max_len`: Maximum output length
- `word_threshold`: Word probability threshold
- `entropy_threshold`: Entropy threshold for filtering

## PiperClient

### Features

- **Voice Management**: List, select, and manage available voices
- **Quality Control**: Different quality levels for speed vs. quality trade-offs
- **SSML Support**: Support for Speech Synthesis Markup Language (planned)
- **Streaming Support**: Real-time audio generation capabilities
- **Voice Configuration**: Per-voice configuration and metadata
- **Model Download**: Automatic voice model downloading

### Usage

```python
from services.clients.piper_client import PiperClient, PiperConfig, PiperQuality

# Basic usage
client = PiperClient()
result = client.synthesize_text("Hello world!")
print(f"Audio saved to: {result['audio_path']}")

# Voice management
voices = client.get_available_voices()
client.set_voice("en_US-lessac-medium")

# Audio buffer generation
result = client.synthesize_to_buffer("Hello world!")
audio_data = result["audio_data"]

# Advanced configuration
config = PiperConfig(
    voice="en_US-lessac-medium",
    quality=PiperQuality.HIGH,
    length_scale=1.0,
    noise_scale=0.667,
    sample_rate=22050
)
client = PiperClient(config)
```

### Configuration Options

- `voice`: Voice model name (e.g., "en_US-lessac-medium")
- `quality`: Voice quality level (LOW, MEDIUM, HIGH, VERY_HIGH)
- `speaker_id`: Speaker ID for multi-speaker models
- `length_scale`: Speech rate control (1.0 = normal)
- `noise_scale`: Noise level for naturalness
- `sentence_silence`: Pause duration between sentences
- `sample_rate`: Output sample rate

## Enhanced ASRService

### Features

- **Whisper Integration**: Uses WhisperClient for robust speech recognition
- **Fallback Support**: Graceful degradation when Whisper is unavailable
- **Multiple Input Types**: File and buffer processing support
- **Model Management**: Easy switching between different Whisper models
- **Detailed Results**: Access to transcription confidence and metadata

### Usage

```python
from services.asr import ASRService
from services.clients.whisper_client import WhisperModel

# Basic usage
asr = ASRService()
transcript = asr.process_audio_file("recording.wav")

# With specific model
asr = ASRService(model=WhisperModel.SMALL)

# Audio buffer processing
audio_buffer = b"..."  # Raw audio data
transcript = asr.process_audio_buffer(audio_buffer)

# Get detailed results
details = asr.get_detailed_transcription("recording.wav")
```

## Enhanced TTSService

### Features

- **Piper Integration**: Primary TTS using high-quality Piper voices
- **Fallback Support**: Automatic fallback to pyttsx3 when Piper unavailable
- **Voice Selection**: Easy voice switching and management
- **Multiple Output Formats**: File and buffer generation
- **Quality Control**: Configurable quality settings

### Usage

```python
from services.tts import TTSService

# Basic usage
tts = TTSService()
audio_path = tts.generate_speech("Hello world!")

# Voice management
voices = tts.get_available_voices()
tts.set_voice("en_US-lessac-medium")

# Buffer generation
audio_buffer = tts.generate_speech_buffer("Hello world!")

# With specific voice
tts = TTSService(voice="en_US-lessac-medium")
```

## Integration with Existing Services

The enhanced clients integrate seamlessly with existing services:

### Command Processor
The `CommandProcessor` automatically uses the enhanced ASR and TTS services:

```python
from services.command_processor import CommandProcessor
from database.manager import DatabaseManager

db_manager = DatabaseManager("aura.db")
processor = CommandProcessor(db_manager)

# Process voice command - uses enhanced ASR/TTS automatically
audio_response, visual_response = processor.process_voice_command("What's the stock of item ABC123?")
```

### WebSocket Server
The server automatically benefits from the enhanced capabilities:

```python
# In server.py - enhanced services used automatically
asr_service = ASRService()  # Uses WhisperClient
command_processor = CommandProcessor(db_manager)  # Uses enhanced TTS
```

## Error Handling and Fallbacks

Both clients implement comprehensive error handling:

### WhisperClient
- Graceful degradation when models are missing
- Timeout handling for long audio files
- Fallback responses for processing errors
- Detailed error reporting

### PiperClient
- Automatic fallback to pyttsx3 when Piper unavailable
- Voice validation before synthesis
- Comprehensive error messages
- Graceful handling of missing models

## Performance Considerations

### Memory Usage
- Audio buffers are processed efficiently without unnecessary copies
- Temporary files are cleaned up automatically
- Models are loaded once and reused

### Processing Speed
- WhisperClient supports multi-threading for faster transcription
- PiperClient provides quality/speed trade-offs
- Buffer processing avoids file I/O overhead

### Scalability
- Both clients support concurrent usage
- Configuration can be adjusted for different performance requirements
- Fallback mechanisms ensure system availability

## Testing

Run the test suite to verify client functionality:

```bash
cd /Users/<USER>/repos/aura
python test_clients.py
```

The test script validates:
- Client initialization and availability
- Basic functionality of both clients
- Service integration
- Error handling and fallback modes

## Future Enhancements

### Planned Features
- **Real-time Processing**: Streaming audio support
- **Model Caching**: Intelligent model loading and caching
- **Voice Cloning**: Custom voice generation capabilities
- **Multi-language Support**: Automatic language detection
- **Quality Metrics**: Transcription confidence scoring
- **Custom Models**: Support for fine-tuned models

### Integration Possibilities
- **WebRTC**: Real-time audio streaming
- **Mobile Apps**: Client SDKs for mobile integration
- **Cloud Services**: Hybrid cloud/local processing
- **Analytics**: Usage and performance monitoring

## Troubleshooting

### Common Issues

1. **Whisper not available**: Ensure whisper.cpp is built and models are downloaded
2. **Piper not available**: Check Piper build and voice model installation
3. **Permission errors**: Verify file system permissions for model directories
4. **Audio format issues**: Ensure audio files are in supported formats (WAV, MP3, etc.)

### Debug Mode
Enable debug logging for detailed troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

This will provide detailed information about client operations and any issues encountered.
