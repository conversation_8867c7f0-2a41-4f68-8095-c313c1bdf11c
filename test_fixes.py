#!/usr/bin/env python3
"""
Test script to verify the fixes for:
1. Default model is phi-3-mini
2. Model switching works
3. Custom SQL generation works
"""

import sys
import logging
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from database.manager import DatabaseManager
from services.nlu import NLUService
from services.command_processor import CommandProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_default_model():
    """Test that the default model is phi-3-mini"""
    print("\n=== Testing Default Model ===")
    try:
        db_manager = DatabaseManager()
        nlu_service = NLUService(db_manager)
        
        current_model = nlu_service.llm_service.get_current_model()
        print(f"Current model: {current_model}")
        
        if current_model == "phi-3-mini":
            print("✅ Default model is correctly set to phi-3-mini")
            return True
        else:
            print(f"❌ Expected phi-3-mini, got {current_model}")
            return False
    except Exception as e:
        print(f"❌ Error testing default model: {e}")
        return False

def test_custom_sql_generation():
    """Test custom SQL generation for analytical questions"""
    print("\n=== Testing Custom SQL Generation ===")
    
    try:
        db_manager = DatabaseManager()
        
        test_questions = [
            "Which items have been sold the most?",
            "What is the average stock level?", 
            "Show me items by category",
            "What's the total inventory value?",
            "Show me recent transactions"
        ]
        
        all_passed = True
        
        for question in test_questions:
            print(f"\nTesting: {question}")
            result = db_manager.execute_custom_query(question)
            
            if result["success"]:
                print(f"✅ Success: Generated SQL and got {result['result_count']} results")
                print(f"   SQL: {result['sql_query'][:100]}...")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing custom SQL: {e}")
        return False

def test_nlu_function_generation():
    """Test that NLU generates the correct function calls"""
    print("\n=== Testing NLU Function Generation ===")
    
    try:
        db_manager = DatabaseManager()
        nlu_service = NLUService(db_manager)
        
        test_cases = [
            ("Which items have been sold the most?", "execute_custom_query"),
            ("What is today?", "get_advice"),
            ("Find item ABC123", "find_item"),
            ("Show me all items", "get_all_items")
        ]
        
        all_passed = True
        
        for command, expected_function in test_cases:
            print(f"\nTesting: {command}")
            result = nlu_service.process_command(command)
            
            actual_function = result.get("function")
            if actual_function == expected_function:
                print(f"✅ Correct function: {actual_function}")
            else:
                print(f"❌ Expected {expected_function}, got {actual_function}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing NLU: {e}")
        return False

def test_function_execution():
    """Test that functions execute correctly without final response generation"""
    print("\n=== Testing Function Execution ===")
    
    try:
        db_manager = DatabaseManager()
        processor = CommandProcessor(db_manager)
        
        # Test execute_custom_query function directly
        nlu_result = {
            "function": "execute_custom_query",
            "params": {"question": "What is the average stock level?"},
            "confidence": 0.8
        }
        
        print("Testing execute_custom_query function...")
        execution_result = processor._execute_function(nlu_result)
        
        if execution_result["success"]:
            print("✅ Function execution successful")
            print(f"   Message: {execution_result['message']}")
            
            # Check if SQL was generated and executed
            result_data = execution_result["result"]
            if result_data.get("sql_query") and result_data.get("results"):
                print(f"   SQL: {result_data['sql_query'][:100]}...")
                print(f"   Results: {len(result_data['results'])} rows")
                return True
            else:
                print("❌ No SQL query or results found")
                return False
        else:
            print(f"❌ Function execution failed: {execution_result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing function execution: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Model and SQL Generation Fixes")
    print("=" * 50)
    
    tests = [
        ("Default Model", test_default_model),
        ("Custom SQL Generation", test_custom_sql_generation),
        ("NLU Function Generation", test_nlu_function_generation),
        ("Function Execution", test_function_execution)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
