'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, MicOff, Volume2, VolumeX, Send, X, Loader2 } from 'lucide-react';

export const VoiceChat = ({ onClose, initialGreeting = "Hello! I'm ready to chat. You can speak or type your message." }) => {
  const [messages, setMessages] = useState([
    { id: '1', text: initialGreeting, sender: 'ai', timestamp: new Date() }
  ]);
  const [textInput, setTextInput] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef(null);
  const textAreaRef = useRef(null);

  const handleSendMessage = useCallback((text, type = 'text') => {
    if (!text.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date(),
      type
    };

    setMessages(prev => [...prev, userMessage]);
    setTextInput('');

    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
    }
  }, []);

  const handleTextSubmit = (e) => {
    e.preventDefault();
    handleSendMessage(textInput);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(textInput);
    }
  };

  const adjustTextAreaHeight = () => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
      textAreaRef.current.style.height = `${Math.min(textAreaRef.current.scrollHeight, 120)}px`;
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={`fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ${
        isMinimized ? 'w-80 h-16' : 'w-96 h-[600px]'
      }`}
    >
      <div className="header">
        <button onClick={() => setIsMinimized(!isMinimized)}>Toggle</button>
        {onClose && <button onClick={onClose}>Close</button>}
      </div>
      <AnimatePresence>
        {!isMinimized && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col h-[calc(100%-4rem)]"
          >
            <div className="messages">
              {messages.map((msg) => (
                <div key={msg.id} className={`message ${msg.sender}`}>{msg.text}</div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            <form onSubmit={handleTextSubmit} className="controls">
              <textarea
                ref={textAreaRef}
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                onKeyDown={handleKeyDown}
                onInput={adjustTextAreaHeight}
                placeholder="Type your message..."
              />
              <button type="submit">Send</button>
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

