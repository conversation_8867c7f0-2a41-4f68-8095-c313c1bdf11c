import React from 'react';
import { Volume2, VolumeX, Minimize2, Maximize2, X } from 'lucide-react';
import { motion } from 'framer-motion';

export const VoiceChatHeader = ({ 
  isMinimized, 
  onMinimize, 
  onClose, 
  isSpeaking,
  title = "AI Assistant" 
}) => {
  return (
    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
      <div className="flex items-center space-x-3">
        <motion.div
          animate={isSpeaking ? { scale: [1, 1.2, 1] } : { scale: 1 }}
          transition={{ duration: 0.5, repeat: isSpeaking ? Infinity : 0 }}
        >
          {isSpeaking ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5 opacity-50" />}
        </motion.div>
        <h3 className="font-semibold">{title}</h3>
      </div>
      
      <div className="flex items-center space-x-2">
        <button
          onClick={onMinimize}
          className="p-1 hover:bg-white/20 rounded transition-colors"
          aria-label={isMinimized ? "Maximize" : "Minimize"}
        >
          {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
        </button>
        
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 hover:bg-white/20 rounded transition-colors"
            aria-label="Close"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};
