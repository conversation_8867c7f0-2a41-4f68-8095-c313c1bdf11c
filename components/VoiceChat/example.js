// Example usage of VoiceChat component
import React, { useState } from 'react';
import { VoiceChat } from './index';

export default function VoiceChatExample() {
  const [showChat, setShowChat] = useState(false);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <h1 className="text-3xl font-bold mb-4">Voice Chat Example</h1>
      
      <button
        onClick={() => setShowChat(!showChat)}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        {showChat ? 'Hide Chat' : 'Show Chat'}
      </button>

      {showChat && (
        <VoiceChat 
          onClose={() => setShowChat(false)}
          initialGreeting="Hello! I'm your AI assistant. How can I help you today?"
          apiEndpoint="/api/chat"
          speechEndpoint="/api/speech"
        />
      )}
    </div>
  );
}
