'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, MicOff, Volume2, VolumeX, Send, X, Loader2, Minimize2, Maximize2 } from 'lucide-react';
import { useVoiceChat } from './useVoiceChat';
import { VoiceChatHeader } from './VoiceChatHeader';
import { VoiceChatMessages } from './VoiceChatMessages';
import { VoiceChatControls } from './VoiceChatControls';

export const VoiceChat = ({ 
  onClose, 
  initialGreeting = "Hello! I'm ready to chat. You can speak or type your message.",
  apiEndpoint = '/api/chat',
  speechEndpoint = '/api/speech'
}) => {
  const [messages, setMessages] = useState([
    { id: '1', text: initialGreeting, sender: 'ai', timestamp: new Date() }
  ]);
  const [textInput, setTextInput] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef(null);
  const textAreaRef = useRef(null);

  const {
    isListening,
    isSpeaking,
    transcript,
    interimTranscript,
    startListening,
    stopListening,
    toggleMute,
    isMuted,
    error,
    clearError,
    speak
  } = useVoiceChat({
    onTranscript: (text) => {
      if (text.trim()) {
        handleSendMessage(text, 'voice');
      }
    }
  });

  const handleSendMessage = useCallback(async (text, type = 'text') => {
    if (!text.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date(),
      type
    };

    setMessages(prev => [...prev, userMessage]);
    setTextInput('');
    setIsProcessing(true);

    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
    }

    try {
      // Send message to API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: text.trim() })
      });

      if (!response.ok) throw new Error('Failed to get response');

      const data = await response.json();
      
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        text: data.response || data.message || 'Sorry, I could not process that.',
        sender: 'ai',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);

      // Speak the response
      if (!isMuted && aiMessage.text) {
        speak(aiMessage.text);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Sorry, I encountered an error. Please try again.',
        sender: 'ai',
        timestamp: new Date(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  }, [apiEndpoint, isMuted, speak]);

  const handleTextSubmit = (e) => {
    e.preventDefault();
    handleSendMessage(textInput);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(textInput);
    }
  };

  const adjustTextAreaHeight = () => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
      textAreaRef.current.style.height = `${Math.min(textAreaRef.current.scrollHeight, 120)}px`;
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={`fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ${
        isMinimized ? 'w-80 h-16' : 'w-96 h-[600px]'
      }`}
    >
      <VoiceChatHeader
        isMinimized={isMinimized}
        onMinimize={() => setIsMinimized(!isMinimized)}
        onClose={onClose}
        isSpeaking={isSpeaking}
        title="AI Assistant"
      />

      <AnimatePresence>
        {!isMinimized && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col h-[calc(100%-4rem)]"
          >
            <VoiceChatMessages
              messages={messages}
              interimTranscript={interimTranscript}
              isProcessing={isProcessing}
              messagesEndRef={messagesEndRef}
            />

            {error && (
              <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                <button
                  onClick={clearError}
                  className="text-xs text-red-500 hover:text-red-700 mt-1"
                >
                  Dismiss
                </button>
              </div>
            )}

            <VoiceChatControls
              isListening={isListening}
              isMuted={isMuted}
              textInput={textInput}
              onStartListening={startListening}
              onStopListening={stopListening}
              onToggleMute={toggleMute}
              onTextChange={setTextInput}
              onTextSubmit={handleTextSubmit}
              onKeyDown={handleKeyDown}
              onTextAreaChange={adjustTextAreaHeight}
              textAreaRef={textAreaRef}
              isProcessing={isProcessing}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
