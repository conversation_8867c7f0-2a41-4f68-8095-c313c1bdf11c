// VoiceChatControls.js
import React from 'react';
import { Mic, MicOff, Send } from 'lucide-react';

export const VoiceChatControls = ({
  isListening,
  isMuted,
  textInput,
  onStartListening,
  onStopListening,
  onToggleMute,
  onTextChange,
  onTextSubmit,
  onKeyDown,
  onTextAreaChange,
  textAreaRef,
  isProcessing
}) => {
  return (
    <div className="flex items-center justify-between p-4 border-t">
      <button
        onClick={isListening ? onStopListening : onStartListening}
        className={`p-2 rounded-full transition-colors ${
          isListening ? 'bg-red-500' : 'bg-blue-500'
        } text-white hover:bg-opacity-80 focus:outline-none`}
        disabled={isProcessing}
        aria-label={isListening ? "Stop Listening" : "Start Listening"}
      >
        {isListening ? <MicOff /> : <Mic />}
      </button>

      <button
        onClick={onToggleMute}
        className={`p-2 rounded-full transition-colors ${isMuted ? 'bg-gray-300' : 'bg-blue-500'} text-white hover:bg-opacity-80 focus:outline-none`}
        aria-label={isMuted ? "Unmute" : "Mute"}
      >
        {isMuted ? <MicOff /> : <Mic />}
      </button>

      <form onSubmit={onTextSubmit} className="flex items-center flex-grow mx-4">
        <textarea
          ref={textAreaRef}
          value={textInput}
          onChange={(e) => onTextChange(e.target.value)}
          onKeyDown={onKeyDown}
          onInput={onTextAreaChange}
          className="flex-grow h-12 p-2 border rounded-lg resize-none focus:outline-none"
          placeholder="Type a message..."
        />
        <button
          type="submit"
          className="p-2 ml-2 text-white bg-blue-500 rounded-full hover:bg-opacity-80 focus:outline-none"
          aria-label="Send Message"
        >
          <Send />
        </button>
      </form>
    </div>
  );
};

