import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';

export const VoiceChatMessages = ({ 
  messages, 
  interimTranscript, 
  isProcessing, 
  messagesEndRef 
}) => {
  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit', 
      hour12: true 
    });
  };

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.map((message, index) => (
        <motion.div
          key={message.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
          className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
        >
          <div
            className={`max-w-[80%] rounded-2xl px-4 py-2 ${
              message.sender === 'user'
                ? 'bg-blue-500 text-white'
                : message.isError
                ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
            }`}
          >
            <p className="text-sm">{message.text}</p>
            <p className={`text-xs mt-1 ${
              message.sender === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
            }`}>
              {formatTime(message.timestamp)}
              {message.type === 'voice' && ' • Voice'}
            </p>
          </div>
        </motion.div>
      ))}

      {interimTranscript && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex justify-end"
        >
          <div className="max-w-[80%] rounded-2xl px-4 py-2 bg-blue-400/50 text-white italic">
            <p className="text-sm">{interimTranscript}</p>
          </div>
        </motion.div>
      )}

      {isProcessing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex justify-start"
        >
          <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl px-4 py-3">
            <Loader2 className="w-4 h-4 animate-spin text-gray-600 dark:text-gray-400" />
          </div>
        </motion.div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};
