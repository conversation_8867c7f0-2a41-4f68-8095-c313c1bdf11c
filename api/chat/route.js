// Example API route for chat functionality (Next.js App Router)
// Place this file in app/api/chat/route.js

export async function POST(request) {
  try {
    const { message } = await request.json();

    // Here you would typically:
    // 1. Process the user message
    // 2. Send it to your AI service (OpenAI, Anthropic, etc.)
    // 3. Return the response

    // For this example, we'll just echo back a simple response
    const response = `I received your message: "${message}". This is a demo response.`;

    return new Response(
      JSON.stringify({ 
        response,
        success: true 
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to process message',
        success: false 
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
