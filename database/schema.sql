-- Aura Inventory Management System Database Schema
-- Created according to PRD specifications

-- Items table - stores current state of each inventory item
CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sku VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    current_quantity INTEGER NOT NULL DEFAULT 0,
    unit_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    reorder_level INTEGER DEFAULT 0,
    supplier VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory transactions table - append-only log for all inventory movements
CREATE TABLE inventory_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    quantity_change INTEGER NOT NULL,
    transaction_type VARCHAR(50) NOT NULL, -- e.g., 'PURCHASE', 'SALE', 'ADJUSTMENT', 'RETURN', 'DAMAGE'
    reference_id VARCHAR(100),
    user_id VARCHAR(50),
    notes TEXT,
    unit_cost DECIMAL(10,2),
    total_value DECIMAL(10,2),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id)
);

-- Database indexes for performance
CREATE INDEX idx_items_sku ON items(sku);
CREATE INDEX idx_items_name ON items(name);
CREATE INDEX idx_transactions_item_id ON inventory_transactions(item_id);
CREATE INDEX idx_transactions_timestamp ON inventory_transactions(timestamp);
CREATE INDEX idx_transactions_type ON inventory_transactions(transaction_type);

-- Insert some sample data for testing
INSERT INTO items (sku, name, category, current_quantity, unit_price, cost_price, reorder_level, supplier, description) VALUES
('WGT-001', 'Widget A', 'Electronics', 100, 29.99, 15.00, 20, 'TechSupply Co', 'Standard widget for electronics assembly'),
('GDT-002', 'Gadget B', 'Electronics', 50, 49.99, 25.00, 10, 'ElectroWorld', 'Advanced gadget with wireless capabilities'),
('BLT-789', 'Large Bolt', 'Hardware', 200, 2.99, 1.50, 50, 'Hardware Plus', 'High-strength steel bolt, 1/2 inch'),
('CAB-456', 'USB Cable', 'Electronics', 75, 9.99, 4.50, 25, 'Cable Direct', 'USB-C to USB-A cable, 6 feet');
