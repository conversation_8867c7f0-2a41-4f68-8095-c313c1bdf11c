import sqlite3
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

class DatabaseManager:
    """Handles all database operations for the Aura inventory system."""
    
    def __init__(self, db_path: str = "./database/aura.db"):
        self.db_path = Path(db_path)
        self.sql_logging_enabled = False  # Initialize to False
        self.sql_log_file = 'logs/sql_queries.log'
        self.initialize_logging()
        
    def initialize_logging(self):
        """Initialize logging settings"""
        config_path = Path('./config.json')
        if config_path.exists():
            with open(config_path, 'r') as config_file:
                config = json.load(config_file)
                logging_config = config.get('logging', {})
                self.sql_logging_enabled = logging_config.get('sql_logging', False)
                self.sql_log_file = logging_config.get('sql_log_file', 'logs/sql_queries.log')
                if self.sql_logging_enabled:
                    # Ensure logs directory exists
                    Path(self.sql_log_file).parent.mkdir(parents=True, exist_ok=True)
                    with open(self.sql_log_file, 'w') as f:
                        f.write(f'# SQL Query Log Started: {datetime.now().isoformat()}\n')

    def log_query(self, query: str, params: tuple):
        """Log SQL query if logging is enabled."""
        if self.sql_logging_enabled:
            log_entry = f'{datetime.now().isoformat()} | QUERY: {query} | PARAMS: {params}\n'
            with open(self.sql_log_file, 'a') as f:
                f.write(log_entry)

    def get_connection(self) -> sqlite3.Connection:
        """Get a database connection."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        return conn

    def execute_query(self, conn: sqlite3.Connection, query: str, params: tuple = ()) -> Any:
        """Execute a given SQL query and log it if logging is enabled."""
        self.log_query(query, params)
        
        # Store the last executed query for context responses
        self.last_executed_queries = getattr(self, 'last_executed_queries', [])
        self.last_executed_queries.append({
            'query': query,
            'params': params,
            'timestamp': datetime.now().isoformat()
        })
        
        # Keep only the last 10 queries
        if len(self.last_executed_queries) > 10:
            self.last_executed_queries = self.last_executed_queries[-10:]
        
        return conn.execute(query, params)
    
    def get_recent_sql_queries(self) -> list:
        """Get recently executed SQL queries for context."""
        return getattr(self, 'last_executed_queries', [])
    
    def clear_sql_query_history(self):
        """Clear the SQL query history."""
        self.last_executed_queries = []
    
    def find_item(self, sku: str = None, name: str = None, item_id: int = None) -> Optional[Dict[str, Any]]:
        """Find an item by SKU, name, or ID."""
        conn = self.get_connection()
        try:
            if item_id:
                cursor = self.execute_query(conn, "SELECT * FROM items WHERE id = ?", (item_id,))
            elif sku:
                cursor = self.execute_query(conn, "SELECT * FROM items WHERE sku = ?", (sku,))
            elif name:
                cursor = self.execute_query(conn, "SELECT * FROM items WHERE name LIKE ?", (f"%{name}%",))
            else:
                return None
            
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            conn.close()
    
    def get_items_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get all items in a specific category."""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM items WHERE category = ?", (category,))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def find_similar_items(self, name: str) -> List[Dict[str, Any]]:
        """Find items with similar sounding names."""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM items WHERE name LIKE ?", (f"%{name}%",))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def get_low_stock_items(self) -> List[Dict[str, Any]]:
        """Get all items that are below their reorder level."""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM items WHERE current_quantity <= reorder_level")
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def get_all_items(self) -> List[Dict[str, Any]]:
        """Get all items in the inventory."""
        conn = self.get_connection()
        try:
            cursor = self.execute_query(conn, "SELECT * FROM items ORDER BY name")
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def create_item(self, sku: str, name: str, category: str = None, 
                   initial_quantity: int = 0, unit_price: float = None, 
                   cost_price: float = None, reorder_level: int = 0, 
                   supplier: str = None, description: str = None) -> Dict[str, Any]:
        """Create a new inventory item."""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                INSERT INTO items (sku, name, category, current_quantity, unit_price, 
                                 cost_price, reorder_level, supplier, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (sku, name, category, initial_quantity, unit_price, cost_price, 
                  reorder_level, supplier, description))
            
            item_id = cursor.lastrowid
            conn.commit()
            
            # Log the initial quantity as a transaction if > 0
            if initial_quantity > 0:
                self._create_transaction(conn, item_id, initial_quantity, 'INITIAL_STOCK', 
                                       user_id='system', notes='Initial inventory creation')
            
            return self.find_item(item_id=item_id)
        finally:
            conn.close()
    
    def update_item_quantity(self, item_id: int, quantity_change: int, 
                           transaction_type: str, reference_id: str = None,
                           user_id: str = 'system', notes: str = None,
                           unit_cost: float = None) -> Dict[str, Any]:
        """Update item quantity and log the transaction."""
        conn = self.get_connection()
        try:
            # Get current item
            cursor = conn.execute("SELECT * FROM items WHERE id = ?", (item_id,))
            item = cursor.fetchone()
            if not item:
                raise ValueError(f"Item with ID {item_id} not found")
            
            # Calculate new quantity
            new_quantity = item['current_quantity'] + quantity_change
            if new_quantity < 0:
                raise ValueError(f"Insufficient stock. Current: {item['current_quantity']}, Requested: {abs(quantity_change)}")
            
            # Update item quantity
            conn.execute("UPDATE items SET current_quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                        (new_quantity, item_id))
            
            # Calculate total value
            total_value = unit_cost * abs(quantity_change) if unit_cost else None
            
            # Log transaction
            self._create_transaction(conn, item_id, quantity_change, transaction_type,
                                   reference_id, user_id, notes, unit_cost, total_value)
            
            conn.commit()
            return self.find_item(item_id=item_id)
        finally:
            conn.close()
    
    def execute_read_only_query(self, query: str) -> List[Dict[str, Any]]:
        """Execute a read-only SQL query securely and return the results."""
        conn = self.get_connection()
        try:
            # Ensure the query is only a SELECT statement
            if not query.lower().strip().startswith("select"):
                raise ValueError("Only SELECT queries are allowed")

            cursor = self.execute_query(conn, query)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()

    def execute_custom_query(self, question: str) -> Dict[str, Any]:
        """Generate and execute a custom SQL query to answer a specific question."""
        try:
            # Generate SQL query using LLM
            sql_query = self._generate_sql_for_question(question)

            if not sql_query:
                return {
                    "success": False,
                    "error": "Could not generate SQL query for the question",
                    "question": question,
                    "sql_query": None,
                    "results": []
                }

            # Execute the generated query
            results = self.execute_read_only_query(sql_query)

            return {
                "success": True,
                "question": question,
                "sql_query": sql_query,
                "results": results,
                "result_count": len(results)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "question": question,
                "sql_query": sql_query if 'sql_query' in locals() else None,
                "results": []
            }

    def _generate_sql_for_question(self, question: str) -> str:
        """Generate SQL query for a given question using database schema knowledge."""
        # Database schema information
        schema_info = """
        Database Schema:

        Table: items
        - id (INTEGER PRIMARY KEY)
        - sku (TEXT UNIQUE)
        - name (TEXT)
        - category (TEXT)
        - current_quantity (INTEGER)
        - reorder_level (INTEGER)
        - unit_cost (REAL)
        - created_at (TIMESTAMP)
        - updated_at (TIMESTAMP)

        Table: inventory_transactions
        - id (INTEGER PRIMARY KEY)
        - item_id (INTEGER REFERENCES items(id))
        - quantity_change (INTEGER)
        - transaction_type (TEXT: PURCHASE, SALE, ADJUSTMENT)
        - reference_id (TEXT)
        - user_id (TEXT)
        - notes (TEXT)
        - unit_cost (REAL)
        - total_value (REAL)
        - created_at (TIMESTAMP)
        """

        # Common SQL patterns for different question types
        sql_patterns = {
            "most sold": """
                SELECT i.name, i.sku, SUM(ABS(t.quantity_change)) as total_sold
                FROM items i
                JOIN inventory_transactions t ON i.id = t.item_id
                WHERE t.transaction_type = 'SALE'
                GROUP BY i.id, i.name, i.sku
                ORDER BY total_sold DESC
                LIMIT 10
            """,
            "average stock": """
                SELECT AVG(current_quantity) as average_stock_level
                FROM items
                WHERE current_quantity > 0
            """,
            "low stock": """
                SELECT name, sku, current_quantity, reorder_level
                FROM items
                WHERE current_quantity <= reorder_level
                ORDER BY current_quantity ASC
            """,
            "recent transactions": """
                SELECT i.name, i.sku, t.transaction_type, t.quantity_change, t.created_at
                FROM items i
                JOIN inventory_transactions t ON i.id = t.item_id
                ORDER BY t.created_at DESC
                LIMIT 20
            """,
            "total value": """
                SELECT SUM(current_quantity * unit_cost) as total_inventory_value
                FROM items
                WHERE current_quantity > 0
            """,
            "by category": """
                SELECT category, COUNT(*) as item_count, SUM(current_quantity) as total_quantity
                FROM items
                GROUP BY category
                ORDER BY item_count DESC
            """,
            "this month": """
                SELECT i.name, i.sku, t.transaction_type, t.quantity_change, t.created_at
                FROM items i
                JOIN inventory_transactions t ON i.id = t.item_id
                WHERE DATE(t.created_at) >= DATE('now', 'start of month')
                ORDER BY t.created_at DESC
            """
        }

        # Simple pattern matching to generate appropriate SQL
        question_lower = question.lower()

        if "most sold" in question_lower or "best selling" in question_lower:
            return sql_patterns["most sold"]
        elif "average stock" in question_lower or "average quantity" in question_lower:
            return sql_patterns["average stock"]
        elif "low stock" in question_lower:
            return sql_patterns["low stock"]
        elif "recent" in question_lower and "transaction" in question_lower:
            return sql_patterns["recent transactions"]
        elif "total value" in question_lower or "inventory value" in question_lower:
            return sql_patterns["total value"]
        elif "category" in question_lower or "categories" in question_lower:
            return sql_patterns["by category"]
        elif "this month" in question_lower or "added this month" in question_lower:
            return sql_patterns["this month"]
        elif "all items" in question_lower:
            return "SELECT name, sku, category, current_quantity, unit_cost FROM items ORDER BY name"
        elif "count" in question_lower and "item" in question_lower:
            return "SELECT COUNT(*) as total_items FROM items"
        else:
            # Default to showing all items for unrecognized questions
            return "SELECT name, sku, category, current_quantity FROM items ORDER BY name LIMIT 20"
        """Create a transaction record."""
        conn.execute("""
            INSERT INTO inventory_transactions 
            (item_id, quantity_change, transaction_type, reference_id, user_id, 
             notes, unit_cost, total_value)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (item_id, quantity_change, transaction_type, reference_id, 
              user_id, notes, unit_cost, total_value))
    
    def get_recent_transactions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent transactions for context."""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                SELECT t.*, i.name as item_name, i.sku as item_sku
                FROM inventory_transactions t
                JOIN items i ON t.item_id = i.id
                ORDER BY t.timestamp DESC
                LIMIT ?
            """, (limit,))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def get_inventory_summary(self) -> Dict[str, Any]:
        """Get a summary of inventory status."""
        conn = self.get_connection()
        try:
            # Total items
            cursor = conn.execute("SELECT COUNT(*) as total_items FROM items")
            total_items = cursor.fetchone()['total_items']
            
            # Total value
            cursor = conn.execute("SELECT SUM(current_quantity * unit_price) as total_value FROM items WHERE unit_price IS NOT NULL")
            total_value = cursor.fetchone()['total_value'] or 0
            
            # Low stock items
            cursor = conn.execute("SELECT COUNT(*) as low_stock_count FROM items WHERE current_quantity <= reorder_level")
            low_stock_count = cursor.fetchone()['low_stock_count']
            
            return {
                'total_items': total_items,
                'total_value': float(total_value),
                'low_stock_count': low_stock_count,
                'timestamp': datetime.now().isoformat()
            }
        finally:
            conn.close()
    
    def generate_llm_context(self) -> Dict[str, Any]:
        """Generate context for LLM processing."""
        return {
            'current_date': datetime.now().isoformat(),
            'available_items': [
                {
                    'sku': item['sku'],
                    'name': item['name'],
                    'category': item['category'],
                    'current_quantity': item['current_quantity'],
                    'reorder_level': item['reorder_level']
                }
                for item in self.get_all_items()
            ],
            'categories': list(set(item['category'] for item in self.get_all_items() if item['category'])),
            'recent_transactions': self.get_recent_transactions(5),
            'low_stock_items': self.get_low_stock_items(),
            'inventory_summary': self.get_inventory_summary()
        }
    
    def get_advice(self, query: str = None) -> str:
        """Get advice or general information about inventory."""
        from datetime import datetime
        
        # Handle date/time questions
        if query and ("what is today" in query.lower() or "today" in query.lower() or "date" in query.lower()):
            return f"Today is {datetime.now().strftime('%A, %B %d, %Y')}"
        
        if query and ("what time" in query.lower() or "time is it" in query.lower()):
            return f"The current time is {datetime.now().strftime('%I:%M %p')}"
        
        # Handle inventory advice
        summary = self.get_inventory_summary()
        low_stock = self.get_low_stock_items()
        
        advice_parts = []
        
        if summary['low_stock_count'] > 0:
            advice_parts.append(f"You have {summary['low_stock_count']} items with low stock that need attention.")
        
        if summary['total_items'] == 0:
            advice_parts.append("Your inventory is empty. Consider adding some items to get started.")
        elif summary['total_value'] > 0:
            advice_parts.append(f"Your inventory contains {summary['total_items']} items with a total value of ${summary['total_value']:.2f}.")
        
        return " ".join(advice_parts) if advice_parts else "Your inventory system is running smoothly."
    
    def _generate_recommendations(self, summary: Dict[str, Any], low_stock: List[Dict[str, Any]]) -> List[str]:
        """Generate basic recommendations based on inventory status."""
        recommendations = []
        
        if summary['low_stock_count'] > 0:
            recommendations.append(f"You have {summary['low_stock_count']} items with low stock that need attention.")
        
        if summary['total_items'] == 0:
            recommendations.append("Your inventory is empty. Consider adding some items to get started.")
        
        if summary['total_value'] > 0:
            recommendations.append(f"Your total inventory value is ${summary['total_value']:.2f}.")
        
        return recommendations
