import json
import logging
from typing import Dict, <PERSON>, <PERSON><PERSON>
from database.manager import DatabaseManager
from services.nlu import NLUService
from services.tts import TTSService

class CommandProcessor:
    """Processes voice commands through the complete pipeline."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.nlu_service = NLUService(db_manager)
        self.tts_service = TTSService()
        self.logger = logging.getLogger(__name__)
    
    def process_voice_command(self, transcript: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Process a voice command and return audio and visual responses."""
        try:
            # Clear SQL query history before processing
            self.db_manager.clear_sql_query_history()

            # Step 1: NLU processing (function call only)
            nlu_result = self.nlu_service.process_command(transcript)

            # Step 2: Execute database function
            execution_result = self._execute_function(nlu_result)

            # Step 3: Generate final response using LLM with function result as context
            final_response = self._generate_final_response(transcript, nlu_result, execution_result)

            # Step 4: Capture SQL queries executed during this command
            sql_queries = self.db_manager.get_recent_sql_queries()

            # Step 5: Generate responses using final LLM response
            audio_response = self._generate_audio_response(execution_result, final_response)
            visual_response = self._generate_visual_response(execution_result, final_response)
            
            # Step 5: Add SQL and LLM context to responses
            audio_response['_sql_queries'] = sql_queries
            audio_response['_llm_prompt'] = nlu_result.get('_llm_prompt')
            audio_response['_llm_raw_response'] = nlu_result.get('_llm_raw_response')
            
            visual_response['_sql_queries'] = sql_queries
            visual_response['_llm_prompt'] = nlu_result.get('_llm_prompt')
            visual_response['_llm_raw_response'] = nlu_result.get('_llm_raw_response')
            
            return audio_response, visual_response
            
        except Exception as e:
            self.logger.error(f"Error processing voice command: {e}")
            error_audio = {
                "type": "AUDIO_RESPONSE",
                "text": f"Sorry, there was an error processing your command: {str(e)}",
                "audio_data": None
            }
            error_visual = {
                "type": "VISUAL_DATA",
                "data_type": "error",
                "payload": {"error": str(e)}
            }
            return error_audio, error_visual

    def _generate_final_response(self, transcript: str, nlu_result: Dict[str, Any], execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final response using LLM with function result as context."""
        try:
            # Access LLM service through NLU service
            if hasattr(self.nlu_service, 'llm_service') and self.nlu_service.llm_service and self.nlu_service.llm_service.is_initialized:
                # Get database context
                context = self.db_manager.generate_llm_context()

                # Generate final response using function result
                final_response = self.nlu_service.llm_service.generate_final_response(
                    transcript,
                    nlu_result,
                    execution_result,
                    context
                )

                if final_response:
                    return final_response

            # Fallback: generate simple response from execution result
            return {
                "text_response": execution_result.get("message", "Operation completed"),
                "visual_html": None
            }

        except Exception as e:
            self.logger.error(f"Error generating final response: {e}")
            return {
                "text_response": execution_result.get("message", "Operation completed"),
                "visual_html": None
            }

    def _execute_function(self, nlu_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the database function based on NLU result."""
        function_name = nlu_result.get("function")
        params = nlu_result.get("params", {})
        
        try:
            if function_name == "find_item":
                result = self.db_manager.find_item(**params)
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": self._generate_item_query_message(result)
                }
            
            elif function_name == "create_item":
                result = self.db_manager.create_item(**params)
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": f"Created new item: {result['name']} (SKU: {result['sku']}) with quantity {result['current_quantity']}"
                }
            
            elif function_name == "update_quantity":
                # First find the item
                item = None
                if "sku" in params:
                    item = self.db_manager.find_item(sku=params["sku"])
                elif "name" in params:
                    item = self.db_manager.find_item(name=params["name"])
                
                if not item:
                    return {
                        "success": False,
                        "function": function_name,
                        "result": None,
                        "message": "Item not found"
                    }
                
                # Update quantity
                result = self.db_manager.update_item_quantity(
                    item_id=item["id"],
                    quantity_change=params["quantity_change"],
                    transaction_type=params["transaction_type"]
                )
                
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": self._generate_update_message(result, params["quantity_change"], params["transaction_type"])
                }
            
            elif function_name == "get_all_items":
                result = self.db_manager.get_all_items()
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": f"Found {len(result)} items in inventory"
                }
            
            elif function_name == "get_items_by_category":
                result = self.db_manager.get_items_by_category(params["category"])
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": f"Found {len(result)} items in {params['category']} category"
                }
            
            elif function_name == "get_low_stock":
                result = self.db_manager.get_low_stock_items()
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": f"Found {len(result)} items with low stock"
                }
            
            elif function_name == "get_advice":
                query = params.get("query", "General inventory advice")
                result = self.db_manager.get_advice(query)
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": result
                }

            elif function_name == "execute_custom_query":
                question = params.get("question", "")
                result = self.db_manager.execute_custom_query(question)

                if result["success"]:
                    message = f"Executed custom query for: {question}"
                    if result["results"]:
                        message += f" (Found {result['result_count']} results)"
                    else:
                        message += " (No results found)"
                else:
                    message = f"Failed to execute query: {result.get('error', 'Unknown error')}"

                return {
                    "success": result["success"],
                    "function": function_name,
                    "result": result,
                    "message": message
                }
            
            elif function_name == "find_similar_item":
                result = self.db_manager.find_similar_items(**params)
                return {
                    "success": True,
                    "function": function_name,
                    "result": result,
                    "message": "Suggested similar items based on your query"
                }
            
            elif function_name == "error":
                return {
                    "success": False,
                    "function": function_name,
                    "result": None,
                    "message": params.get("message", "Unknown error")
                }
            
            else:
                return {
                    "success": False,
                    "function": function_name,
                    "result": None,
                    "message": "Unknown function"
                }
        except Exception as e:
            self.logger.error(f"Error executing function {function_name}: {e}")
            return {
                "success": False,
                "function": function_name,
                "result": None,
                "message": str(e)
            }
    
    def _generate_item_query_message(self, item: Dict[str, Any]) -> str:
        """Generate a message for item query results."""
        if not item:
            return "Item not found"
        
        message = f"{item['name']} (SKU: {item['sku']}) has {item['current_quantity']} units in stock"
        if item['current_quantity'] <= item['reorder_level']:
            message += " - This item is at or below reorder level"
        return message
    
    def _generate_update_message(self, item: Dict[str, Any], quantity_change: int, transaction_type: str) -> str:
        """Generate a message for quantity update results."""
        if quantity_change > 0:
            action = "added" if transaction_type == "PURCHASE" else "adjusted"
            return f"Successfully {action} {quantity_change} units of {item['name']}. Current stock: {item['current_quantity']}"
        else:
            action = "sold" if transaction_type == "SALE" else "removed"
            return f"Successfully {action} {abs(quantity_change)} units of {item['name']}. Current stock: {item['current_quantity']}"
    
    def _generate_audio_response(self, execution_result: Dict[str, Any], final_response: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate audio response message."""
        # Use final LLM response if available, otherwise fallback to standard message
        text = execution_result.get("message", "Operation completed")
        if final_response and final_response.get("text_response"):
            text = final_response["text_response"]

        # Limit text length to prevent huge audio files
        if len(text) > 500:  # Limit to ~500 characters
            text = text[:500] + "..."

        # For large responses, don't generate audio to avoid message size limits
        audio_data = None
        generate_audio = len(text) < 200  # Only generate audio for short responses

        if generate_audio:
            try:
                # Generate TTS audio buffer and convert to base64
                audio_buffer = self.tts_service.generate_speech_buffer(text)

                if audio_buffer:
                    import base64
                    audio_data = f"data:audio/wav;base64,{base64.b64encode(audio_buffer).decode()}"
            except Exception as e:
                self.logger.error(f"Error generating audio: {e}")
                audio_data = None

        return {
            "type": "AUDIO_RESPONSE",
            "text": text,
            "audio_data": audio_data,  # Base64 encoded audio or None
            "success": execution_result.get("success", True),
            "audio_skipped": not generate_audio
        }
    
    def _generate_visual_response(self, execution_result: Dict[str, Any], final_response: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate visual response message."""
        function_name = execution_result.get("function")
        result = execution_result.get("result")

        # Use final LLM response visual_html if available
        if final_response and final_response.get("visual_html"):
            visual_html = final_response["visual_html"]
            return {
                "type": "VISUAL_DATA",
                "data_type": "html",
                "payload": {
                    "html": visual_html,
                    "function": function_name,
                    "result": result
                }
            }
        
        # Fallback to standard visual responses
        if function_name == "find_item" and result:
            return {
                "type": "VISUAL_DATA",
                "data_type": "item_detail",
                "payload": result
            }
        
        elif function_name in ["get_all_items", "get_items_by_category", "get_low_stock", "get_advice"]:
            return {
                "type": "VISUAL_DATA",
                "data_type": "item_list" if function_name != "get_advice" else "advice",
                "payload": result or []
            }
        
        elif function_name in ["create_item", "update_quantity"] and result:
            return {
                "type": "VISUAL_DATA",
                "data_type": "item_detail",
                "payload": result
            }
        
        else:
            return {
                "type": "VISUAL_DATA",
                "data_type": "message",
                "payload": {
                    "message": execution_result.get("message", "Operation completed"),
                    "success": execution_result.get("success", True)
                }
            }


