"""LLM Service for natural language understanding and generation"""

import json
import logging
import asyncio
import concurrent.futures
from functools import partial
from pathlib import Path
from typing import Dict, Any, Optional, List
from services.clients.mlx_client import MLXClient

logger = logging.getLogger(__name__)

def load_config():
    """Load configuration from config.json file."""
    config_path = Path("./config.json")
    if config_path.exists():
        with open(config_path, 'r') as f:
            return json.load(f)
    else:
        logger.warning("config.json not found. Using default configuration.")
        return {
            "defaults": {
                "llm": {"model": "qwen2.5-1.5b", "engine": "mlx", "timeout_seconds": 30, "max_retries": 2},
                "asr": {"model": "mlx-whisper-medium-mlx", "backend_preference": "mlx_whisper"},
                "tts": {"voice": "af_nova"}
            }
        }

class LLMService:
    """Large Language Model service for NLU and text generation"""
    
    def __init__(self, default_model: str = "qwen2.5-1.5b"):
        self.client = MLXClient()
        self.default_model = default_model
        self.is_initialized = False
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)

        # Load timeout configuration
        config = load_config()
        llm_config = config.get('defaults', {}).get('llm', {})
        self.timeout_seconds = llm_config.get('timeout_seconds', 30)
        self.max_retries = llm_config.get('max_retries', 2)
        
    def initialize(self, model_name: str = None):
        """Initialize the MLX model."""
        model_to_load = model_name or self.default_model
        
        try:
            success = self.client.load_model(model_to_load)
            if success:
                self.is_initialized = True
                logger.info(f"LLM service initialized with model: {model_to_load}")
            else:
                logger.error(f"Failed to load LLM model: {model_to_load}")
                raise RuntimeError(f"Failed to load LLM model: {model_to_load}")
        except Exception as e:
            logger.error(f"Failed to initialize LLM service: {e}")
            raise

    def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
    
    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """Get list of available models"""
        return self.client.get_available_models()
    
    def switch_model(self, model_name: str) -> bool:
        """Switch to a different model"""
        try:
            success = self.client.load_model(model_name)
            if success:
                self.is_initialized = True
                logger.info(f"Switched to model: {model_name}")
            return success
        except Exception as e:
            logger.error(f"Error switching model: {e}")
            return False
    
    def get_current_model(self) -> Optional[str]:
        """Get currently loaded model name"""
        return self.client.get_current_model()
    
    def generate_nlu_response(self, transcript: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate NLU response using the LLM."""
        if not self.is_initialized:
            logger.error("LLM service not initialized")
            return self._fallback_response(transcript)

        for attempt in range(self.max_retries + 1):
            try:
                # Create prompt for NLU task
                prompt = self._create_nlu_prompt(transcript, context)

                # Generate response with timeout
                response = self._generate_with_timeout(
                    prompt,
                    max_tokens=500,
                    temperature=0.1
                )

                if response is None:
                    raise TimeoutError(f"LLM generation timed out after {self.timeout_seconds} seconds")

                # Parse the response and include the prompt
                parsed_response = self._parse_nlu_response(response, transcript)
                parsed_response['_llm_prompt'] = prompt  # Add prompt for debugging/context
                parsed_response['_llm_raw_response'] = response  # Add raw response

                return parsed_response

            except (TimeoutError, Exception) as e:
                logger.warning(f"LLM generation attempt {attempt + 1} failed: {e}")
                if attempt == self.max_retries:
                    logger.error(f"All {self.max_retries + 1} LLM generation attempts failed")
                    return self._fallback_response(transcript)

    def _generate_with_timeout(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> Optional[str]:
        """Generate response with timeout handling."""
        try:
            # Run the generation in a thread pool with timeout
            future = self.executor.submit(
                self.client.generate_response,
                prompt,
                max_tokens,
                temperature
            )

            # Wait for result with timeout
            response = future.result(timeout=self.timeout_seconds)
            return response

        except concurrent.futures.TimeoutError:
            logger.error(f"LLM generation timed out after {self.timeout_seconds} seconds")
            return None
        except Exception as e:
            logger.error(f"Error in LLM generation: {e}")
            return None

    def generate_final_response(self, transcript: str, nlu_result: Dict[str, Any], execution_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final response using function execution result as context."""
        if not self.is_initialized:
            logger.error("LLM service not initialized")
            return None

        for attempt in range(self.max_retries + 1):
            try:
                # Create prompt for final response generation
                prompt = self._create_final_response_prompt(transcript, nlu_result, execution_result, context)

                # Generate response with timeout
                response = self._generate_with_timeout(
                    prompt,
                    max_tokens=800,
                    temperature=0.3
                )

                if response is None:
                    raise TimeoutError(f"LLM generation timed out after {self.timeout_seconds} seconds")

                # Parse the response
                parsed_response = self._parse_final_response(response)
                parsed_response['_llm_prompt'] = prompt  # Add prompt for debugging
                parsed_response['_llm_raw_response'] = response  # Add raw response

                return parsed_response

            except (TimeoutError, Exception) as e:
                logger.warning(f"Final response generation attempt {attempt + 1} failed: {e}")
                if attempt == self.max_retries:
                    logger.error(f"All {self.max_retries + 1} final response generation attempts failed")
                    return None
    
    def _create_nlu_prompt(self, transcript: str, context: Dict[str, Any]) -> str:
        """Create a prompt for NLU task."""
        # Get available items and categories for context
        available_items = context.get('available_items', [])
        categories = context.get('categories', [])
        
        # Create items context
        items_context = "\n".join([
            f"- {item['name']} (SKU: {item['sku']}, Category: {item['category']}, Stock: {item['current_quantity']})"
            for item in available_items[:10]  # Limit to first 10 items
        ])

        categories_context = ", ".join(categories)

        # Create messages for chat template
        messages = [
            {
                "role": "system",
"content": """You are an intelligent inventory management assistant. Your task is to understand voice commands and provide comprehensive responses.

Available inventory items:
{items_context}

Available categories: {categories_context}

Your response must be a valid JSON object with the following structure:
{{
  "function": "function_name",
  "params": {{"name": "item_name", "sku": "item_sku", "quantity_change": "0", "transaction_type": ""}},
  "confidence": 0.95
}}

Available functions:
- find_item: Find specific item by name or SKU (params: {"name": "item_name"} OR {"sku": "item_sku"})
- find_similar_item: Find similar sounding items (params: {"name": "partial_name"})
- create_item: Create new item (params: {"sku": "item_sku", "name": "item_name", "category": "category", "initial_quantity": number})
- update_quantity: Update item quantity (params: {"name": "item_name" OR {"sku": "item_sku", "quantity_change": number, "transaction_type": "PURCHASE/SALE/ADJUSTMENT"})
- get_all_items: Get all items in inventory (params: {})
- get_items_by_category: Get items by category (params: {"category": "category_name"})
- get_low_stock: Get items with low stock (params: {})
- get_advice: For general advice, recommendations, date/time questions, or open-ended questions (params: {"query": "your_question"})
- execute_custom_query: For complex questions requiring custom SQL analysis (params: {"question": "your_question"})
- error: Unknown or invalid command (params: {"message": "error_description"})

Transaction types: PURCHASE, SALE, ADJUSTMENT

Parameter Guidelines:
- Always use "name" or "sku" for item identification (NOT "item_identifier")
- For quantity changes, use "quantity_change" (positive for add, negative for remove)
- For similar item searches, use "find_similar_item" function when exact match not found
- Use exact parameter names as specified in function descriptions

Special handling for different question types:
- For date/time questions like "What is today?", "What time is it?", use get_advice function
- For complex inventory analysis like "Which items have been sold the most?", "What's the average stock level?", "Show me items added this month", use execute_custom_query function
- For simple lookups like "Find item ABC123", use find_item function
- Pass the original question as the parameter: {"query": "What is today?"} or {"question": "Which items have been sold the most?"}
- IMPORTANT: Do NOT use placeholders like [current date] in your responses. The actual data will be provided by the function execution.

IMPORTANT:
- Only generate the function call and parameters, NOT the final response
- The final response will be generated after the function executes
- Respond ONLY with valid JSON. Do not include any other text or explanations."""
            },
            {
                "role": "user",
                "content": f"Voice command: \"{transcript}\""
            }
        ]

        # Apply chat template
        try:
            if self.client.tokenizer:
                prompt = self.client.tokenizer.apply_chat_template(
                    messages,
                    add_generation_prompt=True,
                    tokenize=False
                )
                return prompt
            else:
                # Fallback to simple prompt formatting
                system_content = messages[0]["content"]
                user_content = messages[1]["content"]
                return f"{system_content}\n\n{user_content}\n\nResponse:"
        except Exception as e:
            logger.error(f"Error applying chat template: {e}")
            # Fallback to simple prompt
            return f"Voice command: \"{transcript}\". Respond with valid JSON for inventory management."

    def _parse_nlu_response(self, response_text: str, transcript: str) -> Dict[str, Any]:
        """Parse the LLM response into structured format."""
        try:
            # Clean the response text
            response_text = response_text.strip()

            # Try to extract JSON from response
            import re
            json_match = re.search(r'{.*', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)

                # Try to fix incomplete JSON by counting braces
                brace_count = 0
                json_end = len(json_str)

                for i, char in enumerate(json_str):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break

                # If JSON is incomplete, try to close it
                if brace_count > 0:
                    # Find the last complete field
                    lines = json_str.split('\n')
                    complete_lines = []

                    for line in lines:
                        if line.strip() and not line.strip().endswith(',') and not line.strip().endswith('{'):
                            if '":' in line and not line.strip().endswith('"'):
                                # This line is incomplete, try to close it
                                if '"' in line:
                                    line = line.split('"')[0] + '"'
                                complete_lines.append(line)
                                break
                        complete_lines.append(line)

                    # Reconstruct and close JSON
                    json_str = '\n'.join(complete_lines)
                    json_str = json_str.rstrip(',\n') + '\n\n}'
                else:
                    json_str = json_str[:json_end]

                # Try to parse the JSON
                try:
                    parsed = json.loads(json_str)
                except json.JSONDecodeError:
                    # If that fails, try with basic required fields only
                    basic_match = re.search(r'"function":\s*"([^"]+)"', response_text)
                    if basic_match:
                        function_name = basic_match.group(1)

                        # Try to extract confidence
                        confidence_match = re.search(r'"confidence":\s*([0-9.]+)', response_text)
                        confidence = float(confidence_match.group(1)) if confidence_match else 0.8

                        # Try to extract text_response
                        text_match = re.search(r'"text_response":\s*"([^"]+)"', response_text)
                        text_response = text_match.group(1) if text_match else ""

                        parsed = {
                            "function": function_name,
                            "params": {},
                            "confidence": confidence
                        }
                    else:
                        raise json.JSONDecodeError("Could not extract basic fields", response_text, 0)

                # Validate required fields
                if "function" in parsed:
                    return {
                        "function": parsed.get("function"),
                        "params": parsed.get("params", {}),
                        "confidence": parsed.get("confidence", 0.8),
                        "response": None  # Will be generated after execution
                    }

            logger.warning(f"Could not parse LLM response: {response_text}")
            return self._fallback_response(transcript)

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}")
            logger.error(f"Raw response: {response_text}")
            return self._fallback_response(transcript)
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return self._fallback_response(transcript)

    def _fallback_response(self, transcript: str) -> Dict[str, Any]:
        """Fallback response when LLM fails."""
        return {
            "function": "error",
            "params": {"message": "Could not understand command"},
            "confidence": 0.1,
            "response": "I'm sorry, I couldn't understand that command. Please try rephrasing."
        }

    def _create_final_response_prompt(self, transcript: str, nlu_result: Dict[str, Any], execution_result: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Create prompt for final response generation."""
        function_name = nlu_result.get("function", "unknown")
        function_result = execution_result.get("result")
        success = execution_result.get("success", True)

        prompt = f"""You are an AI assistant for an inventory management system. A user asked: "{transcript}"

The system executed function: {function_name}
Function result: {function_result}
Success: {success}

Database context:
{json.dumps(context, indent=2)}

Generate a natural, helpful response to the user's question using the function result.
Respond with JSON containing:
- "text_response": Natural language response to the user
- "visual_html": Optional HTML for visual display (or null)

Example:
{{
    "text_response": "I found 5 items in stock. The current inventory includes...",
    "visual_html": "<div class='inventory-summary'>...</div>"
}}

Response:"""

        return prompt

    def _parse_final_response(self, response_text: str) -> Dict[str, Any]:
        """Parse final response from LLM."""
        try:
            # Clean the response text
            response_text = response_text.strip()

            # Try to extract JSON from response
            import re
            json_match = re.search(r'{[^}]*}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)

                # Try to fix common JSON issues
                try:
                    parsed = json.loads(json_str)
                    return {
                        "text_response": parsed.get("text_response", "Operation completed"),
                        "visual_html": parsed.get("visual_html")
                    }
                except json.JSONDecodeError:
                    # Try to extract text_response manually
                    text_match = re.search(r'"text_response":\s*"([^"]*)"', json_str)
                    if text_match:
                        return {
                            "text_response": text_match.group(1),
                            "visual_html": None
                        }

            # Fallback: treat entire response as text
            return {
                "text_response": response_text if response_text else "Operation completed",
                "visual_html": None
            }

        except Exception as e:
            logger.error(f"Error parsing final response: {e}")
            return {
                "text_response": "Operation completed",
                "visual_html": None
            }

