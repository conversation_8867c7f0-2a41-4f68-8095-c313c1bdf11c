import logging
from typing import Dict, Any
from database.manager import DatabaseManager
from services.llm_service import LLMService

class NLUService:
    """Natural Language Understanding service for processing voice commands."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.llm_service = LLMService()

        # Initialize LLM service (required - no fallback)
        try:
            self.llm_service.initialize()
            self.logger.info("NLU service initialized with LLM support")
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM service: {e}")
            raise RuntimeError(f"NLU service requires LLM support. Failed to initialize: {e}")
    
    def process_command(self, transcript: str) -> Dict[str, Any]:
        """Process a voice command transcript and return structured function call."""
        try:
            # Get dynamic context for LLM
            context = self.db_manager.generate_llm_context()

            # Use LLM for NLU processing
            result = self.llm_service.generate_nlu_response(transcript, context)
            self.logger.info(f"LLM processing successful for: {transcript}")
            return result

        except Exception as e:
            self.logger.error(f"Error processing command: {e}")
            return {
                "function": "error",
                "params": {"message": str(e)},
                "confidence": 0.0,
                "response": None
            }


