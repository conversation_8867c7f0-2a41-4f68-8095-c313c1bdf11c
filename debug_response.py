#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.command_processor import CommandProcessor
from database.manager import DatabaseManager

async def test_response():
    """Test what's happening with the response generation."""
    
    print("Initializing services...")
    db = DatabaseManager('database/aura.db')
    processor = CommandProcessor(db)
    
    print("Testing voice command processing...")
    
    try:
        # Test with a simple command
        transcript = "What is my current stock?"
        print(f"Processing: {transcript}")
        
        audio_resp, visual_resp = await processor.process_voice_command(transcript)
        
        print("\n=== AUDIO RESPONSE ===")
        print(f"Type: {audio_resp.get('type')}")
        print(f"Success: {audio_resp.get('success')}")
        print(f"Audio skipped: {audio_resp.get('audio_skipped')}")
        print(f"Text length: {len(audio_resp.get('text', ''))}")
        print(f"Text (first 200 chars): {repr(audio_resp.get('text', '')[:200])}")
        
        print("\n=== VISUAL RESPONSE ===")
        print(f"Type: {visual_resp.get('type')}")
        print(f"Data type: {visual_resp.get('data_type')}")
        
        # Check if there are any LLM debug fields
        if '_llm_raw_response' in audio_resp:
            llm_raw = audio_resp['_llm_raw_response']
            print(f"\n=== LLM RAW RESPONSE ===")
            print(f"Length: {len(llm_raw) if llm_raw else 0}")
            print(f"First 200 chars: {repr(llm_raw[:200]) if llm_raw else 'None'}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_response())
